"""
股票相似性分析工具
通过DeepSeek API分析股票业务相似性，输出相似股票推荐
"""

import pandas as pd
import numpy as np
from openai import OpenAI
import time
import json
import os
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockSimilarityAnalyzer:
    def __init__(self):
        """初始化股票相似性分析器"""
        self.key_db = "21125d99-0230-4988-9567-40c9bb6dacbd"
        self.client_db = OpenAI(
            api_key=self.key_db,
            base_url="https://ark.cn-beijing.volces.com/api/v3",
        )
        
    def get_response_deep_seek_db_r1(self, query: str, temperature: float = 0.7) -> str:
        """
        调用DeepSeek API获取响应
        
        Args:
            query: 查询内容
            temperature: 温度参数，控制回答的随机性
            
        Returns:
            API响应内容
        """
        try:
            completion = self.client_db.chat.completions.create(
                model="ep-20250205060440-7pvfl",
                messages=[
                    {"role": "user", "content": query},
                ],
                temperature=temperature,
            )
            return completion.choices[0].message.content
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return str(e)
    
    def create_similarity_query(self, stock_code: str, stock_name: str) -> str:
        """
        创建股票相似性分析的查询prompt
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            
        Returns:
            格式化的查询字符串
        """
        query = f"""
请分析股票 {stock_code}({stock_name}) 的业务特点，并找出A股市场中与其业务最相似的一只股票。

要求：
1. 分析该股票的主营业务、行业特点、商业模式
2. 在A股市场中找出业务最相似的股票
3. 说明相似的具体原因（从业务模式、行业地位、产品结构等角度）

请按以下JSON格式返回结果：
{{
    "original_stock": {{
        "code": "{stock_code}",
        "name": "{stock_name}"
    }},
    "similar_stock": {{
        "code": "相似股票代码",
        "name": "相似股票简称"
    }},
    "similarity_reason": "详细的相似原因说明",
    "confidence": "相似度评分(1-10分)"
}}

请确保返回的是有效的JSON格式。
"""
        return query
    
    def parse_api_response(self, response: str) -> Dict:
        """
        解析API响应，提取结构化数据
        
        Args:
            response: API响应字符串
            
        Returns:
            解析后的字典数据
        """
        try:
            # 尝试直接解析JSON
            if response.strip().startswith('{'):
                return json.loads(response)
            
            # 如果不是直接的JSON，尝试提取JSON部分
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            
            # 如果无法解析JSON，返回默认结构
            return {
                "similar_stock": {"code": "未知", "name": "解析失败"},
                "similarity_reason": response,
                "confidence": "0"
            }
            
        except Exception as e:
            logger.error(f"解析API响应失败: {e}")
            return {
                "similar_stock": {"code": "错误", "name": "解析异常"},
                "similarity_reason": f"解析错误: {str(e)}",
                "confidence": "0"
            }
    
    def analyze_single_stock(self, stock_code: str, stock_name: str, 
                           max_retries: int = 3, delay: float = 1.0) -> Dict:
        """
        分析单只股票的相似性
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            max_retries: 最大重试次数
            delay: 重试间隔时间
            
        Returns:
            分析结果字典
        """
        query = self.create_similarity_query(stock_code, stock_name)
        
        for attempt in range(max_retries):
            try:
                logger.info(f"正在分析股票 {stock_code}({stock_name})，第{attempt+1}次尝试")
                
                response = self.get_response_deep_seek_db_r1(query, temperature=0.3)
                result = self.parse_api_response(response)
                
                # 验证结果有效性
                if (result.get("similar_stock", {}).get("code") not in ["未知", "错误", None] and
                    result.get("similarity_reason") and
                    len(result.get("similarity_reason", "")) > 10):
                    
                    logger.info(f"成功分析股票 {stock_code}")
                    return result
                
                logger.warning(f"第{attempt+1}次分析结果质量不佳，准备重试")
                
            except Exception as e:
                logger.error(f"第{attempt+1}次分析失败: {e}")
            
            if attempt < max_retries - 1:
                time.sleep(delay)
        
        # 所有重试都失败，返回错误结果
        return {
            "similar_stock": {"code": "分析失败", "name": "重试次数超限"},
            "similarity_reason": "API调用失败或解析错误",
            "confidence": "0"
        }
    
    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            DataFrame对象
        """
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功读取Excel文件: {file_path}，共{len(df)}行数据")
            return df
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def process_stock_list(self, input_file: str, output_file: str = None) -> pd.DataFrame:
        """
        处理股票列表，进行相似性分析
        
        Args:
            input_file: 输入Excel文件路径
            output_file: 输出CSV文件路径（可选）
            
        Returns:
            包含分析结果的DataFrame
        """
        # 读取输入文件
        df = self.read_excel_file(input_file)
        
        # 验证必要的列是否存在
        required_columns = ['股票代码', '股票名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            # 尝试其他可能的列名
            column_mapping = {
                '代码': '股票代码',
                'code': '股票代码', 
                'Code': '股票代码',
                '股票代码': '股票代码',
                '名称': '股票名称',
                'name': '股票名称',
                'Name': '股票名称', 
                '股票名称': '股票名称',
                '简称': '股票名称'
            }
            
            for old_name, new_name in column_mapping.items():
                if old_name in df.columns:
                    df = df.rename(columns={old_name: new_name})
            
            # 再次检查
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Excel文件缺少必要的列: {missing_columns}")
        
        # 初始化结果列
        df['相似股票代码'] = ''
        df['相似股票简称'] = ''
        df['相似原因'] = ''
        df['相似度评分'] = ''
        
        # 逐行处理
        total_stocks = len(df)
        for index, row in df.iterrows():
            stock_code = str(row['股票代码']).strip()
            stock_name = str(row['股票名称']).strip()
            
            logger.info(f"处理进度: {index+1}/{total_stocks} - {stock_code}({stock_name})")
            
            # 分析相似性
            result = self.analyze_single_stock(stock_code, stock_name)
            
            # 更新结果
            df.at[index, '相似股票代码'] = result.get('similar_stock', {}).get('code', '')
            df.at[index, '相似股票简称'] = result.get('similar_stock', {}).get('name', '')
            df.at[index, '相似原因'] = result.get('similarity_reason', '')
            df.at[index, '相似度评分'] = result.get('confidence', '')
            
            # 添加延迟避免API限制
            time.sleep(0.5)
        
        # 保存结果
        if output_file is None:
            output_file = f"股票相似性分析结果_{time.strftime('%Y%m%d_%H%M%S')}.csv"
        
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"分析完成，结果已保存到: {output_file}")
        
        return df

def main():
    """主函数"""
    analyzer = StockSimilarityAnalyzer()
    
    # 示例用法
    input_file = "股票列表.xlsx"  # 输入Excel文件
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        print("请确保Excel文件存在，且包含'股票代码'和'股票名称'列")
        return
    
    try:
        result_df = analyzer.process_stock_list(input_file)
        print(f"分析完成！共处理{len(result_df)}只股票")
        print("结果已保存为CSV文件")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"执行失败: {e}")

if __name__ == "__main__":
    main()
