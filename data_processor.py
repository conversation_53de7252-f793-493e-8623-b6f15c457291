"""
数据处理模块
处理Excel读取、CSV输出、数据验证等功能
"""

import pandas as pd
import numpy as np
import os
import re
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.supported_formats = ['.xlsx', '.xls', '.csv']
        
    def validate_stock_code(self, code: str) -> bool:
        """
        验证股票代码格式
        
        Args:
            code: 股票代码
            
        Returns:
            是否为有效的股票代码
        """
        if not isinstance(code, str):
            code = str(code)
        
        code = code.strip()
        
        # A股股票代码格式：6位数字
        if re.match(r'^\d{6}$', code):
            return True
        
        # 带交易所后缀的格式：000001.SZ, 600000.SH
        if re.match(r'^\d{6}\.(SZ|SH)$', code.upper()):
            return True
            
        return False
    
    def clean_stock_code(self, code: str) -> str:
        """
        清理股票代码格式
        
        Args:
            code: 原始股票代码
            
        Returns:
            清理后的股票代码
        """
        if not isinstance(code, str):
            code = str(code)
        
        code = code.strip().upper()
        
        # 移除可能的前缀（如SH、SZ）
        code = re.sub(r'^(SH|SZ)', '', code)
        
        # 移除可能的后缀（如.SH、.SZ）
        code = re.sub(r'\.(SH|SZ)$', '', code)
        
        # 确保是6位数字，不足的前面补0
        if code.isdigit():
            code = code.zfill(6)
        
        return code
    
    def validate_dataframe(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证DataFrame的数据质量
        
        Args:
            df: 待验证的DataFrame
            
        Returns:
            (是否通过验证, 错误信息列表)
        """
        errors = []
        
        # 检查必要的列
        required_columns = ['股票代码', '股票名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            errors.append(f"缺少必要的列: {missing_columns}")
        
        if not errors:  # 只有在列存在的情况下才进行数据验证
            # 检查空值
            for col in required_columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    errors.append(f"列'{col}'存在{null_count}个空值")
            
            # 检查股票代码格式
            invalid_codes = []
            for idx, code in enumerate(df['股票代码']):
                if not self.validate_stock_code(str(code)):
                    invalid_codes.append(f"第{idx+1}行: {code}")
            
            if invalid_codes:
                errors.append(f"无效的股票代码格式: {invalid_codes[:5]}")  # 只显示前5个
        
        return len(errors) == 0, errors
    
    def read_input_file(self, file_path: str) -> pd.DataFrame:
        """
        读取输入文件（支持Excel和CSV）
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame对象
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_ext}，支持的格式: {self.supported_formats}")
        
        try:
            if file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_ext == '.csv':
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                
                if df is None:
                    raise ValueError("无法读取CSV文件，请检查文件编码")
            
            logger.info(f"成功读取文件: {file_path}，共{len(df)}行数据")
            return df
            
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            raise
    
    def normalize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        
        Args:
            df: 原始DataFrame
            
        Returns:
            列名标准化后的DataFrame
        """
        column_mapping = {
            # 股票代码的各种可能名称
            '代码': '股票代码',
            'code': '股票代码',
            'Code': '股票代码',
            'CODE': '股票代码',
            '证券代码': '股票代码',
            '股票代码': '股票代码',
            
            # 股票名称的各种可能名称
            '名称': '股票名称',
            'name': '股票名称',
            'Name': '股票名称',
            'NAME': '股票名称',
            '股票名称': '股票名称',
            '证券名称': '股票名称',
            '简称': '股票名称',
            '股票简称': '股票名称',
            '证券简称': '股票名称'
        }
        
        # 应用列名映射
        df_normalized = df.copy()
        for old_name, new_name in column_mapping.items():
            if old_name in df_normalized.columns:
                df_normalized = df_normalized.rename(columns={old_name: new_name})
        
        return df_normalized
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清理后的DataFrame
        """
        df_clean = df.copy()
        
        # 标准化列名
        df_clean = self.normalize_column_names(df_clean)
        
        # 清理股票代码
        if '股票代码' in df_clean.columns:
            df_clean['股票代码'] = df_clean['股票代码'].apply(
                lambda x: self.clean_stock_code(str(x)) if pd.notna(x) else x
            )
        
        # 清理股票名称（去除前后空格）
        if '股票名称' in df_clean.columns:
            df_clean['股票名称'] = df_clean['股票名称'].apply(
                lambda x: str(x).strip() if pd.notna(x) else x
            )
        
        # 移除完全重复的行
        original_count = len(df_clean)
        df_clean = df_clean.drop_duplicates()
        removed_count = original_count - len(df_clean)
        
        if removed_count > 0:
            logger.info(f"移除了{removed_count}行重复数据")
        
        return df_clean
    
    def save_results(self, df: pd.DataFrame, output_path: str, 
                    include_timestamp: bool = True) -> str:
        """
        保存结果到文件
        
        Args:
            df: 要保存的DataFrame
            output_path: 输出文件路径
            include_timestamp: 是否在文件名中包含时间戳
            
        Returns:
            实际保存的文件路径
        """
        if include_timestamp:
            import time
            base_name, ext = os.path.splitext(output_path)
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            output_path = f"{base_name}_{timestamp}{ext}"
        
        try:
            # 根据文件扩展名选择保存方式
            file_ext = os.path.splitext(output_path)[1].lower()
            
            if file_ext == '.csv':
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
            elif file_ext in ['.xlsx', '.xls']:
                df.to_excel(output_path, index=False)
            else:
                # 默认保存为CSV
                output_path = output_path + '.csv'
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            logger.info(f"结果已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise
    
    def create_summary_report(self, df: pd.DataFrame) -> Dict:
        """
        创建分析结果摘要报告
        
        Args:
            df: 分析结果DataFrame
            
        Returns:
            摘要报告字典
        """
        report = {
            'total_stocks': len(df),
            'successful_analysis': 0,
            'failed_analysis': 0,
            'average_confidence': 0,
            'top_similar_stocks': [],
            'analysis_errors': []
        }
        
        if '相似股票代码' in df.columns:
            # 统计成功和失败的分析
            successful = df[df['相似股票代码'].notna() & 
                          (df['相似股票代码'] != '') & 
                          (~df['相似股票代码'].isin(['未知', '错误', '分析失败']))]
            
            report['successful_analysis'] = len(successful)
            report['failed_analysis'] = len(df) - len(successful)
        
        if '相似度评分' in df.columns:
            # 计算平均相似度评分
            numeric_scores = pd.to_numeric(df['相似度评分'], errors='coerce')
            valid_scores = numeric_scores.dropna()
            if len(valid_scores) > 0:
                report['average_confidence'] = round(valid_scores.mean(), 2)
        
        if '相似股票简称' in df.columns:
            # 统计最常出现的相似股票
            similar_stocks = df['相似股票简称'].value_counts().head(5)
            report['top_similar_stocks'] = similar_stocks.to_dict()
        
        return report
    
    def export_summary_report(self, report: Dict, output_path: str = None) -> str:
        """
        导出摘要报告
        
        Args:
            report: 摘要报告字典
            output_path: 输出文件路径
            
        Returns:
            报告文件路径
        """
        if output_path is None:
            import time
            output_path = f"分析摘要报告_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("股票相似性分析摘要报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"总股票数量: {report['total_stocks']}\n")
                f.write(f"成功分析: {report['successful_analysis']}\n")
                f.write(f"分析失败: {report['failed_analysis']}\n")
                f.write(f"成功率: {report['successful_analysis']/report['total_stocks']*100:.1f}%\n")
                f.write(f"平均相似度评分: {report['average_confidence']}\n\n")
                
                if report['top_similar_stocks']:
                    f.write("最常出现的相似股票:\n")
                    for stock, count in report['top_similar_stocks'].items():
                        f.write(f"  {stock}: {count}次\n")
            
            logger.info(f"摘要报告已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"保存摘要报告失败: {e}")
            raise
