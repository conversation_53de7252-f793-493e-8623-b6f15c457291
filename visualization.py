"""
可视化模块
创建股票相似性分析结果的可视化界面
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from typing import Dict, List, Optional
import logging
from wordcloud import WordCloud
import jieba

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class StockSimilarityVisualizer:
    """股票相似性分析可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def create_confidence_distribution(self, df: pd.DataFrame, save_path: str = None) -> str:
        """
        创建相似度评分分布图
        
        Args:
            df: 分析结果DataFrame
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        if '相似度评分' not in df.columns:
            logger.warning("数据中没有相似度评分列")
            return None
        
        # 转换为数值类型
        scores = pd.to_numeric(df['相似度评分'], errors='coerce').dropna()
        
        if len(scores) == 0:
            logger.warning("没有有效的相似度评分数据")
            return None
        
        # 创建分布图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 直方图
        ax1.hist(scores, bins=10, alpha=0.7, color=self.colors[0], edgecolor='black')
        ax1.set_xlabel('相似度评分')
        ax1.set_ylabel('频次')
        ax1.set_title('相似度评分分布')
        ax1.grid(True, alpha=0.3)
        
        # 箱线图
        ax2.boxplot(scores, vert=True)
        ax2.set_ylabel('相似度评分')
        ax2.set_title('相似度评分箱线图')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path is None:
            import time
            save_path = f"相似度评分分布_{time.strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"相似度分布图已保存到: {save_path}")
        return save_path
    
    def create_similar_stocks_ranking(self, df: pd.DataFrame, top_n: int = 10, 
                                    save_path: str = None) -> str:
        """
        创建最常出现的相似股票排行榜
        
        Args:
            df: 分析结果DataFrame
            top_n: 显示前N名
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        if '相似股票简称' not in df.columns:
            logger.warning("数据中没有相似股票简称列")
            return None
        
        # 统计相似股票出现频次
        similar_counts = df['相似股票简称'].value_counts().head(top_n)
        
        if len(similar_counts) == 0:
            logger.warning("没有有效的相似股票数据")
            return None
        
        # 创建横向条形图
        fig, ax = plt.subplots(figsize=(12, 8))
        
        bars = ax.barh(range(len(similar_counts)), similar_counts.values, 
                      color=self.colors[:len(similar_counts)])
        
        ax.set_yticks(range(len(similar_counts)))
        ax.set_yticklabels(similar_counts.index)
        ax.set_xlabel('出现次数')
        ax.set_title(f'最常出现的相似股票 (Top {top_n})')
        
        # 在条形图上添加数值标签
        for i, (bar, value) in enumerate(zip(bars, similar_counts.values)):
            ax.text(value + 0.1, i, str(value), va='center')
        
        ax.grid(True, alpha=0.3, axis='x')
        plt.tight_layout()
        
        if save_path is None:
            import time
            save_path = f"相似股票排行榜_{time.strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"相似股票排行榜已保存到: {save_path}")
        return save_path
    
    def create_similarity_heatmap(self, df: pd.DataFrame, save_path: str = None) -> str:
        """
        创建股票相似性热力图
        
        Args:
            df: 分析结果DataFrame
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        if not all(col in df.columns for col in ['股票简称', '相似股票简称', '相似度评分']):
            logger.warning("数据中缺少必要的列用于创建热力图")
            return None
        
        # 准备数据
        df_viz = df[['股票简称', '相似股票简称', '相似度评分']].copy()
        df_viz['相似度评分'] = pd.to_numeric(df_viz['相似度评分'], errors='coerce')
        df_viz = df_viz.dropna()
        
        if len(df_viz) == 0:
            logger.warning("没有有效的数据用于创建热力图")
            return None
        
        # 创建透视表
        pivot_table = df_viz.pivot_table(
            index='股票简称', 
            columns='相似股票简称', 
            values='相似度评分', 
            aggfunc='mean'
        )
        
        # 只显示有足够数据的股票
        min_data_points = 2
        pivot_table = pivot_table.loc[
            pivot_table.count(axis=1) >= min_data_points,
            pivot_table.count(axis=0) >= min_data_points
        ]
        
        if pivot_table.empty:
            logger.warning("数据不足以创建有意义的热力图")
            return None
        
        # 创建热力图
        fig, ax = plt.subplots(figsize=(12, 10))
        
        sns.heatmap(pivot_table, annot=True, cmap='YlOrRd', 
                   fmt='.1f', ax=ax, cbar_kws={'label': '相似度评分'})
        
        ax.set_title('股票相似性热力图')
        ax.set_xlabel('相似股票')
        ax.set_ylabel('原始股票')
        
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        if save_path is None:
            import time
            save_path = f"相似性热力图_{time.strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"相似性热力图已保存到: {save_path}")
        return save_path
    
    def create_similarity_network(self, df: pd.DataFrame, save_path: str = None) -> str:
        """
        创建股票相似性网络图（使用Plotly）
        
        Args:
            df: 分析结果DataFrame
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        try:
            import networkx as nx
        except ImportError:
            logger.warning("需要安装networkx库来创建网络图: pip install networkx")
            return None
        
        if not all(col in df.columns for col in ['股票简称', '相似股票简称', '相似度评分']):
            logger.warning("数据中缺少必要的列用于创建网络图")
            return None
        
        # 准备数据
        df_viz = df[['股票简称', '相似股票简称', '相似度评分']].copy()
        df_viz['相似度评分'] = pd.to_numeric(df_viz['相似度评分'], errors='coerce')
        df_viz = df_viz.dropna()
        df_viz = df_viz[df_viz['相似度评分'] >= 6]  # 只显示相似度较高的连接
        
        if len(df_viz) == 0:
            logger.warning("没有足够高相似度的数据用于创建网络图")
            return None
        
        # 创建网络图
        G = nx.Graph()
        
        for _, row in df_viz.iterrows():
            G.add_edge(row['股票简称'], row['相似股票简称'], 
                      weight=row['相似度评分'])
        
        # 计算布局
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # 准备节点数据
        node_x = []
        node_y = []
        node_text = []
        node_size = []
        
        for node in G.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            node_text.append(node)
            node_size.append(G.degree(node) * 10 + 10)  # 根据连接数调整大小
        
        # 准备边数据
        edge_x = []
        edge_y = []
        
        for edge in G.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
        
        # 创建Plotly图
        fig = go.Figure()
        
        # 添加边
        fig.add_trace(go.Scatter(x=edge_x, y=edge_y,
                                line=dict(width=1, color='#888'),
                                hoverinfo='none',
                                mode='lines',
                                name='连接'))
        
        # 添加节点
        fig.add_trace(go.Scatter(x=node_x, y=node_y,
                                mode='markers+text',
                                marker=dict(size=node_size,
                                          color='lightblue',
                                          line=dict(width=2, color='darkblue')),
                                text=node_text,
                                textposition="middle center",
                                hoverinfo='text',
                                name='股票'))
        
        fig.update_layout(title='股票相似性网络图',
                         titlefont_size=16,
                         showlegend=False,
                         hovermode='closest',
                         margin=dict(b=20,l=5,r=5,t=40),
                         annotations=[ dict(
                             text="节点大小表示连接数量，只显示相似度≥6的连接",
                             showarrow=False,
                             xref="paper", yref="paper",
                             x=0.005, y=-0.002,
                             xanchor='left', yanchor='bottom',
                             font=dict(size=12)
                         )],
                         xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                         yaxis=dict(showgrid=False, zeroline=False, showticklabels=False))
        
        if save_path is None:
            import time
            save_path = f"相似性网络图_{time.strftime('%Y%m%d_%H%M%S')}.html"
        
        fig.write_html(save_path)
        
        logger.info(f"相似性网络图已保存到: {save_path}")
        return save_path
    
    def create_reason_wordcloud(self, df: pd.DataFrame, save_path: str = None) -> str:
        """
        创建相似原因词云图
        
        Args:
            df: 分析结果DataFrame
            save_path: 保存路径
            
        Returns:
            图表文件路径
        """
        if '相似原因' not in df.columns:
            logger.warning("数据中没有相似原因列")
            return None
        
        # 合并所有相似原因文本
        reasons_text = ' '.join(df['相似原因'].dropna().astype(str))
        
        if not reasons_text.strip():
            logger.warning("没有有效的相似原因文本")
            return None
        
        # 使用jieba分词
        words = jieba.cut(reasons_text)
        words_list = [word for word in words if len(word) > 1 and word not in ['相似', '股票', '公司', '业务']]
        words_text = ' '.join(words_list)
        
        # 创建词云
        wordcloud = WordCloud(
            font_path='simhei.ttf',  # 需要中文字体文件
            width=800, height=600,
            background_color='white',
            max_words=100,
            colormap='viridis'
        ).generate(words_text)
        
        # 绘制词云图
        plt.figure(figsize=(12, 8))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('相似原因词云图', fontsize=16, pad=20)
        
        if save_path is None:
            import time
            save_path = f"相似原因词云_{time.strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"相似原因词云图已保存到: {save_path}")
        return save_path
    
    def create_comprehensive_dashboard(self, df: pd.DataFrame, output_dir: str = None) -> Dict[str, str]:
        """
        创建综合仪表板（生成所有可视化图表）
        
        Args:
            df: 分析结果DataFrame
            output_dir: 输出目录
            
        Returns:
            生成的图表文件路径字典
        """
        if output_dir is None:
            import time
            output_dir = f"可视化结果_{time.strftime('%Y%m%d_%H%M%S')}"
        
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        results = {}
        
        # 生成各种图表
        charts = [
            ('confidence_distribution', self.create_confidence_distribution),
            ('similar_stocks_ranking', self.create_similar_stocks_ranking),
            ('similarity_heatmap', self.create_similarity_heatmap),
            ('similarity_network', self.create_similarity_network),
            ('reason_wordcloud', self.create_reason_wordcloud)
        ]
        
        for chart_name, chart_func in charts:
            try:
                save_path = os.path.join(output_dir, f"{chart_name}.png")
                if chart_name == 'similarity_network':
                    save_path = os.path.join(output_dir, f"{chart_name}.html")
                
                result_path = chart_func(df, save_path)
                if result_path:
                    results[chart_name] = result_path
                    
            except Exception as e:
                logger.error(f"创建{chart_name}图表失败: {e}")
        
        logger.info(f"综合仪表板已创建，共生成{len(results)}个图表")
        return results
