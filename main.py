"""
股票相似性分析工具主程序
整合所有功能模块，提供完整的分析和可视化功能
"""

import os
import sys
import logging
import argparse
from typing import Optional
import pandas as pd

from stock_similarity_analyzer import StockSimilarityAnalyzer
from data_processor import DataProcessor
from visualization import StockSimilarityVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StockAnalysisApp:
    """股票相似性分析应用主类"""
    
    def __init__(self):
        """初始化应用"""
        self.analyzer = StockSimilarityAnalyzer()
        self.data_processor = DataProcessor()
        self.visualizer = StockSimilarityVisualizer()
    
    def run_analysis(self, input_file: str, output_file: str = None, 
                    enable_visualization: bool = True) -> dict:
        """
        运行完整的股票相似性分析
        
        Args:
            input_file: 输入Excel文件路径
            output_file: 输出CSV文件路径
            enable_visualization: 是否启用可视化
            
        Returns:
            分析结果摘要
        """
        logger.info("开始股票相似性分析...")
        
        try:
            # 1. 读取和清理数据
            logger.info("步骤1: 读取和清理数据")
            df_raw = self.data_processor.read_input_file(input_file)
            df_clean = self.data_processor.clean_data(df_raw)
            
            # 验证数据质量
            is_valid, errors = self.data_processor.validate_dataframe(df_clean)
            if not is_valid:
                logger.error(f"数据验证失败: {errors}")
                return {"success": False, "errors": errors}
            
            logger.info(f"数据清理完成，共{len(df_clean)}条有效记录")
            
            # 2. 执行相似性分析
            logger.info("步骤2: 执行相似性分析")
            
            # 初始化结果列
            df_clean['相似股票代码'] = ''
            df_clean['相似股票简称'] = ''
            df_clean['相似原因'] = ''
            df_clean['相似度评分'] = ''
            
            total_stocks = len(df_clean)
            successful_count = 0
            
            for index, row in df_clean.iterrows():
                stock_code = str(row['股票代码']).strip()
                stock_name = str(row['股票名称']).strip()
                
                logger.info(f"分析进度: {index+1}/{total_stocks} - {stock_code}({stock_name})")
                
                # 分析单只股票
                result = self.analyzer.analyze_single_stock(stock_code, stock_name)
                
                # 更新结果
                df_clean.at[index, '相似股票代码'] = result.get('similar_stock', {}).get('code', '')
                df_clean.at[index, '相似股票简称'] = result.get('similar_stock', {}).get('name', '')
                df_clean.at[index, '相似原因'] = result.get('similarity_reason', '')
                df_clean.at[index, '相似度评分'] = result.get('confidence', '')
                
                # 检查是否成功
                if (result.get('similar_stock', {}).get('code') not in ['未知', '错误', '分析失败', ''] and
                    result.get('similarity_reason') and
                    len(result.get('similarity_reason', '')) > 10):
                    successful_count += 1
                
                # 添加延迟避免API限制
                import time
                time.sleep(0.5)
            
            logger.info(f"相似性分析完成，成功率: {successful_count}/{total_stocks} ({successful_count/total_stocks*100:.1f}%)")
            
            # 3. 保存结果
            logger.info("步骤3: 保存分析结果")
            if output_file is None:
                output_file = "股票相似性分析结果.csv"
            
            result_file = self.data_processor.save_results(df_clean, output_file)
            
            # 4. 生成摘要报告
            logger.info("步骤4: 生成摘要报告")
            summary_report = self.data_processor.create_summary_report(df_clean)
            report_file = self.data_processor.export_summary_report(summary_report)
            
            # 5. 创建可视化（如果启用）
            visualization_files = {}
            if enable_visualization:
                logger.info("步骤5: 创建可视化图表")
                try:
                    visualization_files = self.visualizer.create_comprehensive_dashboard(df_clean)
                    logger.info(f"可视化完成，生成{len(visualization_files)}个图表")
                except Exception as e:
                    logger.error(f"可视化创建失败: {e}")
            
            # 返回结果摘要
            return {
                "success": True,
                "total_stocks": total_stocks,
                "successful_analysis": successful_count,
                "success_rate": successful_count/total_stocks*100,
                "result_file": result_file,
                "report_file": report_file,
                "visualization_files": visualization_files,
                "summary": summary_report
            }
            
        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return {"success": False, "error": str(e)}
    
    def print_results_summary(self, results: dict):
        """打印结果摘要"""
        if not results.get("success"):
            print(f"❌ 分析失败: {results.get('error', '未知错误')}")
            return
        
        print("\n" + "="*60)
        print("📊 股票相似性分析完成！")
        print("="*60)
        
        print(f"📈 总股票数量: {results['total_stocks']}")
        print(f"✅ 成功分析: {results['successful_analysis']}")
        print(f"📊 成功率: {results['success_rate']:.1f}%")
        
        print(f"\n📁 输出文件:")
        print(f"   • 分析结果: {results['result_file']}")
        print(f"   • 摘要报告: {results['report_file']}")
        
        if results['visualization_files']:
            print(f"\n🎨 可视化图表:")
            for chart_name, file_path in results['visualization_files'].items():
                print(f"   • {chart_name}: {file_path}")
        
        # 显示摘要统计
        summary = results['summary']
        if summary.get('top_similar_stocks'):
            print(f"\n🏆 最常出现的相似股票:")
            for stock, count in list(summary['top_similar_stocks'].items())[:5]:
                print(f"   • {stock}: {count}次")
        
        print(f"\n📊 平均相似度评分: {summary.get('average_confidence', 0)}")
        print("\n分析完成！请查看输出文件获取详细结果。")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票相似性分析工具')
    parser.add_argument('--input', '-i', type=str, default='股票列表.xlsx',
                       help='输入Excel文件路径 (默认: 股票列表.xlsx)')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出CSV文件路径 (默认: 自动生成)')
    parser.add_argument('--no-viz', action='store_true',
                       help='禁用可视化功能')
    parser.add_argument('--create-sample', action='store_true',
                       help='创建示例数据文件')
    
    args = parser.parse_args()
    
    # 创建示例数据
    if args.create_sample:
        from create_sample_data import create_sample_excel
        create_sample_excel()
        print("示例数据文件已创建，可以使用 --input 股票列表.xlsx 开始分析")
        return
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"❌ 错误: 找不到输入文件 '{args.input}'")
        print("💡 提示: 使用 --create-sample 创建示例数据文件")
        print("📋 输入文件应包含'股票代码'和'股票名称'列")
        return
    
    # 创建应用实例并运行分析
    app = StockAnalysisApp()
    
    print("🚀 开始股票相似性分析...")
    print(f"📂 输入文件: {args.input}")
    print(f"🎨 可视化: {'禁用' if args.no_viz else '启用'}")
    
    results = app.run_analysis(
        input_file=args.input,
        output_file=args.output,
        enable_visualization=not args.no_viz
    )
    
    # 打印结果摘要
    app.print_results_summary(results)

if __name__ == "__main__":
    main()
